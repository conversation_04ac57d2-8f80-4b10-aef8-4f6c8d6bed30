import 'package:shared_preferences/shared_preferences.dart';
import '../../domain/entities/weather.dart';
import '../../domain/entities/forecast.dart';
import '../../domain/entities/location.dart';
import '../../domain/repositories/weather_repository.dart';
import '../../core/constants.dart';
import '../../core/errors.dart';
import '../datasources/weather_api_service.dart';
import '../models/weather_model.dart';
import '../models/forecast_model.dart';
import '../models/location_model.dart';

class WeatherRepositoryImpl implements WeatherRepository {
  final WeatherApiService _apiService;
  final SharedPreferences _prefs;

  WeatherRepositoryImpl({
    required WeatherApiService apiService,
    required SharedPreferences prefs,
  }) : _apiService = apiService, _prefs = prefs;

  String get _apiKey {
    final apiKey = _prefs.getString(AppConstants.apiKeyStorageKey);
    if (apiKey == null || apiKey.isEmpty) {
      throw const ApiKeyException('API key not found. Please set your OpenWeatherMap API key in settings.');
    }
    return apiKey;
  }

  @override
  Future<Weather> getCurrentWeatherByCoordinates({
    required double latitude,
    required double longitude,
    String units = 'metric',
    String language = 'en',
  }) async {
    try {
      final weatherModel = await _apiService.getCurrentWeatherByCoordinates(
        latitude: latitude,
        longitude: longitude,
        apiKey: _apiKey,
        units: units,
        language: language,
      );
      return weatherModel.toEntity();
    } catch (e) {
      if (e is AppException) rethrow;
      throw ServerException('Failed to get current weather: $e');
    }
  }

  @override
  Future<Weather> getCurrentWeatherByCity({
    required String cityName,
    String units = 'metric',
    String language = 'en',
  }) async {
    try {
      final weatherModel = await _apiService.getCurrentWeatherByCity(
        cityName: cityName,
        apiKey: _apiKey,
        units: units,
        language: language,
      );
      return weatherModel.toEntity();
    } catch (e) {
      if (e is AppException) rethrow;
      throw ServerException('Failed to get current weather: $e');
    }
  }

  @override
  Future<Forecast> getForecastByCoordinates({
    required double latitude,
    required double longitude,
    String units = 'metric',
    String language = 'en',
  }) async {
    try {
      final forecastModel = await _apiService.getForecastByCoordinates(
        latitude: latitude,
        longitude: longitude,
        apiKey: _apiKey,
        units: units,
        language: language,
      );
      return forecastModel.toEntity();
    } catch (e) {
      if (e is AppException) rethrow;
      throw ServerException('Failed to get forecast: $e');
    }
  }

  @override
  Future<Forecast> getForecastByCity({
    required String cityName,
    String units = 'metric',
    String language = 'en',
  }) async {
    try {
      final forecastModel = await _apiService.getForecastByCity(
        cityName: cityName,
        apiKey: _apiKey,
        units: units,
        language: language,
      );
      return forecastModel.toEntity();
    } catch (e) {
      if (e is AppException) rethrow;
      throw ServerException('Failed to get forecast: $e');
    }
  }

  @override
  Future<List<Location>> searchCities(String query) async {
    try {
      final locationModels = await _apiService.searchCities(
        query: query,
        apiKey: _apiKey,
      );
      return locationModels.map((model) => model.toEntity()).toList();
    } catch (e) {
      if (e is AppException) rethrow;
      throw ServerException('Failed to search cities: $e');
    }
  }

  @override
  Future<Location> getLocationByCoordinates({
    required double latitude,
    required double longitude,
  }) async {
    try {
      final locationModel = await _apiService.getLocationByCoordinates(
        latitude: latitude,
        longitude: longitude,
        apiKey: _apiKey,
      );
      return locationModel.toEntity();
    } catch (e) {
      if (e is AppException) rethrow;
      throw ServerException('Failed to get location: $e');
    }
  }

  @override
  Future<void> clearCache() async {
    try {
      // Clear any cached weather data
      await _prefs.remove('cached_weather_data');
      await _prefs.remove('cached_forecast_data');
      await _prefs.remove('cache_timestamp');
    } catch (e) {
      throw CacheException('Failed to clear cache: $e');
    }
  }

  @override
  Future<bool> isCacheValid() async {
    try {
      final timestamp = _prefs.getInt('cache_timestamp');
      if (timestamp == null) return false;
      
      final cacheTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
      final now = DateTime.now();
      final difference = now.difference(cacheTime);
      
      // Cache is valid for 10 minutes
      return difference.inMinutes < 10;
    } catch (e) {
      return false;
    }
  }

  // Helper method to save API key
  Future<void> saveApiKey(String apiKey) async {
    await _prefs.setString(AppConstants.apiKeyStorageKey, apiKey);
  }

  // Helper method to get API key
  String? getApiKey() {
    return _prefs.getString(AppConstants.apiKeyStorageKey);
  }

  // Helper method to check if API key is set
  bool hasApiKey() {
    final apiKey = _prefs.getString(AppConstants.apiKeyStorageKey);
    return apiKey != null && apiKey.isNotEmpty;
  }
}
