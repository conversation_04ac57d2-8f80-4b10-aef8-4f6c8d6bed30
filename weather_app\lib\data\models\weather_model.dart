import '../../domain/entities/weather.dart';

class WeatherModel extends Weather {
  const WeatherModel({
    required super.temperature,
    required super.feelsLike,
    required super.tempMin,
    required super.tempMax,
    required super.pressure,
    required super.humidity,
    required super.windSpeed,
    required super.windDirection,
    super.windGust,
    required super.visibility,
    super.uvIndex,
    required super.cloudiness,
    required super.condition,
    required super.description,
    required super.iconCode,
    required super.dateTime,
    required super.sunrise,
    required super.sunset,
  });

  factory WeatherModel.fromJson(Map<String, dynamic> json) {
    final main = json['main'] ?? {};
    final weather = (json['weather'] as List?)?.first ?? {};
    final wind = json['wind'] ?? {};
    final sys = json['sys'] ?? {};
    final clouds = json['clouds'] ?? {};

    return WeatherModel(
      temperature: (main['temp'] ?? 0.0).toDouble(),
      feelsLike: (main['feels_like'] ?? 0.0).toDouble(),
      tempMin: (main['temp_min'] ?? 0.0).toDouble(),
      tempMax: (main['temp_max'] ?? 0.0).toDouble(),
      pressure: (main['pressure'] ?? 0).toInt(),
      humidity: (main['humidity'] ?? 0).toInt(),
      windSpeed: (wind['speed'] ?? 0.0).toDouble(),
      windDirection: (wind['deg'] ?? 0).toInt(),
      windGust: wind['gust']?.toDouble(),
      visibility: ((json['visibility'] ?? 10000) / 1000).toDouble(), // Convert to km
      uvIndex: json['uvi']?.toDouble(),
      cloudiness: (clouds['all'] ?? 0).toInt(),
      condition: weather['main'] ?? '',
      description: weather['description'] ?? '',
      iconCode: weather['icon'] ?? '',
      dateTime: DateTime.fromMillisecondsSinceEpoch((json['dt'] ?? 0) * 1000),
      sunrise: DateTime.fromMillisecondsSinceEpoch((sys['sunrise'] ?? 0) * 1000),
      sunset: DateTime.fromMillisecondsSinceEpoch((sys['sunset'] ?? 0) * 1000),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'main': {
        'temp': temperature,
        'feels_like': feelsLike,
        'temp_min': tempMin,
        'temp_max': tempMax,
        'pressure': pressure,
        'humidity': humidity,
      },
      'weather': [
        {
          'main': condition,
          'description': description,
          'icon': iconCode,
        }
      ],
      'wind': {
        'speed': windSpeed,
        'deg': windDirection,
        if (windGust != null) 'gust': windGust,
      },
      'clouds': {
        'all': cloudiness,
      },
      'visibility': (visibility * 1000).toInt(), // Convert back to meters
      if (uvIndex != null) 'uvi': uvIndex,
      'dt': dateTime.millisecondsSinceEpoch ~/ 1000,
      'sys': {
        'sunrise': sunrise.millisecondsSinceEpoch ~/ 1000,
        'sunset': sunset.millisecondsSinceEpoch ~/ 1000,
      },
    };
  }

  factory WeatherModel.fromEntity(Weather weather) {
    return WeatherModel(
      temperature: weather.temperature,
      feelsLike: weather.feelsLike,
      tempMin: weather.tempMin,
      tempMax: weather.tempMax,
      pressure: weather.pressure,
      humidity: weather.humidity,
      windSpeed: weather.windSpeed,
      windDirection: weather.windDirection,
      windGust: weather.windGust,
      visibility: weather.visibility,
      uvIndex: weather.uvIndex,
      cloudiness: weather.cloudiness,
      condition: weather.condition,
      description: weather.description,
      iconCode: weather.iconCode,
      dateTime: weather.dateTime,
      sunrise: weather.sunrise,
      sunset: weather.sunset,
    );
  }

  Weather toEntity() {
    return Weather(
      temperature: temperature,
      feelsLike: feelsLike,
      tempMin: tempMin,
      tempMax: tempMax,
      pressure: pressure,
      humidity: humidity,
      windSpeed: windSpeed,
      windDirection: windDirection,
      windGust: windGust,
      visibility: visibility,
      uvIndex: uvIndex,
      cloudiness: cloudiness,
      condition: condition,
      description: description,
      iconCode: iconCode,
      dateTime: dateTime,
      sunrise: sunrise,
      sunset: sunset,
    );
  }

  @override
  WeatherModel copyWith({
    double? temperature,
    double? feelsLike,
    double? tempMin,
    double? tempMax,
    int? pressure,
    int? humidity,
    double? windSpeed,
    int? windDirection,
    double? windGust,
    int? visibility,
    double? uvIndex,
    int? cloudiness,
    String? condition,
    String? description,
    String? iconCode,
    DateTime? dateTime,
    DateTime? sunrise,
    DateTime? sunset,
  }) {
    return WeatherModel(
      temperature: temperature ?? this.temperature,
      feelsLike: feelsLike ?? this.feelsLike,
      tempMin: tempMin ?? this.tempMin,
      tempMax: tempMax ?? this.tempMax,
      pressure: pressure ?? this.pressure,
      humidity: humidity ?? this.humidity,
      windSpeed: windSpeed ?? this.windSpeed,
      windDirection: windDirection ?? this.windDirection,
      windGust: windGust ?? this.windGust,
      visibility: visibility?.toDouble() ?? this.visibility,
      uvIndex: uvIndex ?? this.uvIndex,
      cloudiness: cloudiness ?? this.cloudiness,
      condition: condition ?? this.condition,
      description: description ?? this.description,
      iconCode: iconCode ?? this.iconCode,
      dateTime: dateTime ?? this.dateTime,
      sunrise: sunrise ?? this.sunrise,
      sunset: sunset ?? this.sunset,
    );
  }
}
