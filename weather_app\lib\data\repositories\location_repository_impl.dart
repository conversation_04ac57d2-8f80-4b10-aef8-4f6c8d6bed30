import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../../domain/entities/location.dart';
import '../../domain/repositories/location_repository.dart';
import '../../core/constants.dart';
import '../../core/errors.dart';
import '../datasources/location_service.dart';
import '../models/location_model.dart';

class LocationRepositoryImpl implements LocationRepository {
  final LocationService _locationService;
  final SharedPreferences _prefs;

  LocationRepositoryImpl({
    required LocationService locationService,
    required SharedPreferences prefs,
  }) : _locationService = locationService, _prefs = prefs;

  @override
  Future<Location> getCurrentLocation() async {
    try {
      final locationModel = await _locationService.getCurrentPosition();
      return locationModel.toEntity();
    } catch (e) {
      if (e is AppException) rethrow;
      throw LocationException('Failed to get current location: $e');
    }
  }

  @override
  Future<Location?> getLastKnownLocation() async {
    try {
      final locationModel = await _locationService.getLastKnownPosition();
      return locationModel?.toEntity();
    } catch (e) {
      if (e is AppException) rethrow;
      throw LocationException('Failed to get last known location: $e');
    }
  }

  @override
  Stream<Location> getLocationStream() {
    try {
      return _locationService.getPositionStream()
          .map((locationModel) => locationModel.toEntity());
    } catch (e) {
      throw LocationException('Failed to get location stream: $e');
    }
  }

  @override
  Future<bool> isLocationServiceEnabled() async {
    try {
      return await _locationService.isLocationServiceEnabled();
    } catch (e) {
      throw LocationServiceException('Failed to check location service: $e');
    }
  }

  @override
  Future<bool> isPermissionGranted() async {
    try {
      return await _locationService.isPermissionGranted();
    } catch (e) {
      throw LocationPermissionException('Failed to check permission: $e');
    }
  }

  @override
  Future<bool> requestPermission() async {
    try {
      final permission = await _locationService.requestPermission();
      return await _locationService.handlePermissionResult(permission);
    } catch (e) {
      throw LocationPermissionException('Failed to request permission: $e');
    }
  }

  @override
  Future<void> openLocationSettings() async {
    try {
      await _locationService.openLocationSettings();
    } catch (e) {
      throw LocationServiceException('Failed to open location settings: $e');
    }
  }

  @override
  Future<void> openAppSettings() async {
    try {
      await _locationService.openAppSettings();
    } catch (e) {
      throw LocationServiceException('Failed to open app settings: $e');
    }
  }

  @override
  Future<List<Location>> getSavedLocations() async {
    try {
      final locationsJson = _prefs.getStringList(AppConstants.savedLocationsKey) ?? [];
      return locationsJson
          .map((json) => LocationModel.fromJson(jsonDecode(json)).toEntity())
          .toList();
    } catch (e) {
      throw CacheException('Failed to get saved locations: $e');
    }
  }

  @override
  Future<void> saveLocation(Location location) async {
    try {
      final savedLocations = await getSavedLocations();
      
      // Check if location already exists
      final exists = savedLocations.any((saved) =>
          saved.latitude == location.latitude &&
          saved.longitude == location.longitude);
      
      if (!exists) {
        savedLocations.add(location);
        final locationsJson = savedLocations
            .map((loc) => jsonEncode(LocationModel.fromEntity(loc).toJson()))
            .toList();
        await _prefs.setStringList(AppConstants.savedLocationsKey, locationsJson);
      }
    } catch (e) {
      throw CacheException('Failed to save location: $e');
    }
  }

  @override
  Future<void> removeLocation(Location location) async {
    try {
      final savedLocations = await getSavedLocations();
      savedLocations.removeWhere((saved) =>
          saved.latitude == location.latitude &&
          saved.longitude == location.longitude);
      
      final locationsJson = savedLocations
          .map((loc) => jsonEncode(LocationModel.fromEntity(loc).toJson()))
          .toList();
      await _prefs.setStringList(AppConstants.savedLocationsKey, locationsJson);
    } catch (e) {
      throw CacheException('Failed to remove location: $e');
    }
  }

  @override
  Future<void> clearSavedLocations() async {
    try {
      await _prefs.remove(AppConstants.savedLocationsKey);
    } catch (e) {
      throw CacheException('Failed to clear saved locations: $e');
    }
  }

  @override
  double calculateDistance(Location from, Location to) {
    try {
      return _locationService.calculateDistance(
        startLatitude: from.latitude,
        startLongitude: from.longitude,
        endLatitude: to.latitude,
        endLongitude: to.longitude,
      );
    } catch (e) {
      throw LocationException('Failed to calculate distance: $e');
    }
  }

  @override
  bool isValidCoordinates(double latitude, double longitude) {
    return _locationService.isValidCoordinates(latitude, longitude);
  }

  // Helper method to check if location is saved
  Future<bool> isLocationSaved(Location location) async {
    try {
      final savedLocations = await getSavedLocations();
      return savedLocations.any((saved) =>
          saved.latitude == location.latitude &&
          saved.longitude == location.longitude);
    } catch (e) {
      return false;
    }
  }

  // Helper method to get nearest saved location
  Future<Location?> getNearestSavedLocation(Location currentLocation) async {
    try {
      final savedLocations = await getSavedLocations();
      if (savedLocations.isEmpty) return null;

      Location? nearest;
      double minDistance = double.infinity;

      for (final location in savedLocations) {
        final distance = calculateDistance(currentLocation, location);
        if (distance < minDistance) {
          minDistance = distance;
          nearest = location;
        }
      }

      return nearest;
    } catch (e) {
      return null;
    }
  }
}
