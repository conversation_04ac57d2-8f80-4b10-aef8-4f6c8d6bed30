import 'package:geolocator/geolocator.dart';
import '../../core/errors.dart';
import '../models/location_model.dart';

class LocationService {
  // Check if location services are enabled
  Future<bool> isLocationServiceEnabled() async {
    try {
      return await Geolocator.isLocationServiceEnabled();
    } catch (e) {
      throw const LocationServiceException('Failed to check location service status');
    }
  }

  // Check location permission status
  Future<LocationPermission> checkPermission() async {
    try {
      return await Geolocator.checkPermission();
    } catch (e) {
      throw const LocationPermissionException('Failed to check location permission');
    }
  }

  // Request location permission
  Future<LocationPermission> requestPermission() async {
    try {
      return await Geolocator.requestPermission();
    } catch (e) {
      throw const LocationPermissionException('Failed to request location permission');
    }
  }

  // Get current position
  Future<LocationModel> getCurrentPosition({
    LocationAccuracy accuracy = LocationAccuracy.high,
    Duration? timeLimit,
  }) async {
    try {
      // Check if location services are enabled
      final serviceEnabled = await isLocationServiceEnabled();
      if (!serviceEnabled) {
        throw const LocationServiceException('Location services are disabled');
      }

      // Check permission
      LocationPermission permission = await checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await requestPermission();
        if (permission == LocationPermission.denied) {
          throw const LocationPermissionException('Location permission denied');
        }
      }

      if (permission == LocationPermission.deniedForever) {
        throw const LocationPermissionException(
          'Location permissions are permanently denied, we cannot request permissions.'
        );
      }

      // Get position
      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: accuracy,
        timeLimit: timeLimit ?? const Duration(seconds: 15),
      );

      return LocationModel.currentLocation(
        latitude: position.latitude,
        longitude: position.longitude,
      );
    } on LocationServiceException {
      rethrow;
    } on LocationPermissionException {
      rethrow;
    } catch (e) {
      if (e.toString().contains('timeout')) {
        throw const LocationException('Location request timeout');
      }
      throw LocationException('Failed to get current location: $e');
    }
  }

  // Get last known position
  Future<LocationModel?> getLastKnownPosition() async {
    try {
      final position = await Geolocator.getLastKnownPosition();
      if (position != null) {
        return LocationModel.currentLocation(
          latitude: position.latitude,
          longitude: position.longitude,
        );
      }
      return null;
    } catch (e) {
      throw LocationException('Failed to get last known position: $e');
    }
  }

  // Calculate distance between two locations
  double calculateDistance({
    required double startLatitude,
    required double startLongitude,
    required double endLatitude,
    required double endLongitude,
  }) {
    try {
      return Geolocator.distanceBetween(
        startLatitude,
        startLongitude,
        endLatitude,
        endLongitude,
      );
    } catch (e) {
      throw LocationException('Failed to calculate distance: $e');
    }
  }

  // Calculate bearing between two locations
  double calculateBearing({
    required double startLatitude,
    required double startLongitude,
    required double endLatitude,
    required double endLongitude,
  }) {
    try {
      return Geolocator.bearingBetween(
        startLatitude,
        startLongitude,
        endLatitude,
        endLongitude,
      );
    } catch (e) {
      throw LocationException('Failed to calculate bearing: $e');
    }
  }

  // Stream of position updates
  Stream<LocationModel> getPositionStream({
    LocationSettings? locationSettings,
  }) {
    try {
      final settings = locationSettings ?? const LocationSettings(
        accuracy: LocationAccuracy.high,
        distanceFilter: 10, // Update every 10 meters
      );

      return Geolocator.getPositionStream(locationSettings: settings)
          .map((position) => LocationModel.currentLocation(
                latitude: position.latitude,
                longitude: position.longitude,
              ));
    } catch (e) {
      throw LocationException('Failed to get position stream: $e');
    }
  }

  // Open location settings
  Future<bool> openLocationSettings() async {
    try {
      return await Geolocator.openLocationSettings();
    } catch (e) {
      throw const LocationServiceException('Failed to open location settings');
    }
  }

  // Open app settings
  Future<bool> openAppSettings() async {
    try {
      return await Geolocator.openAppSettings();
    } catch (e) {
      throw const LocationServiceException('Failed to open app settings');
    }
  }

  // Check if location permission is granted
  Future<bool> isPermissionGranted() async {
    try {
      final permission = await checkPermission();
      return permission == LocationPermission.always ||
             permission == LocationPermission.whileInUse;
    } catch (e) {
      return false;
    }
  }

  // Get location permission status as string
  String getPermissionStatusString(LocationPermission permission) {
    switch (permission) {
      case LocationPermission.denied:
        return 'denied';
      case LocationPermission.deniedForever:
        return 'deniedForever';
      case LocationPermission.whileInUse:
        return 'whileInUse';
      case LocationPermission.always:
        return 'always';
      case LocationPermission.unableToDetermine:
        return 'unableToDetermine';
    }
  }

  // Handle location permission result
  Future<bool> handlePermissionResult(LocationPermission permission) async {
    switch (permission) {
      case LocationPermission.denied:
        // Request permission again
        final newPermission = await requestPermission();
        return newPermission == LocationPermission.always ||
               newPermission == LocationPermission.whileInUse;
      
      case LocationPermission.deniedForever:
        // Open app settings
        await openAppSettings();
        return false;
      
      case LocationPermission.whileInUse:
      case LocationPermission.always:
        return true;
      
      case LocationPermission.unableToDetermine:
        return false;
    }
  }

  // Validate coordinates
  bool isValidCoordinates(double latitude, double longitude) {
    return latitude >= -90 && latitude <= 90 &&
           longitude >= -180 && longitude <= 180;
  }
}
