import '../../domain/entities/forecast.dart';

class ForecastItemModel extends ForecastItem {
  const ForecastItemModel({
    required super.dateTime,
    required super.temperature,
    required super.tempMin,
    required super.tempMax,
    required super.condition,
    required super.description,
    required super.iconCode,
    required super.humidity,
    required super.windSpeed,
    required super.pressure,
    required super.cloudiness,
    super.precipitationProbability,
  });

  factory ForecastItemModel.fromJson(Map<String, dynamic> json) {
    final main = json['main'] ?? {};
    final weather = (json['weather'] as List?)?.first ?? {};
    final wind = json['wind'] ?? {};
    final clouds = json['clouds'] ?? {};

    return ForecastItemModel(
      dateTime: DateTime.fromMillisecondsSinceEpoch((json['dt'] ?? 0) * 1000),
      temperature: (main['temp'] ?? 0.0).toDouble(),
      tempMin: (main['temp_min'] ?? 0.0).toDouble(),
      tempMax: (main['temp_max'] ?? 0.0).toDouble(),
      condition: weather['main'] ?? '',
      description: weather['description'] ?? '',
      iconCode: weather['icon'] ?? '',
      humidity: (main['humidity'] ?? 0).toInt(),
      windSpeed: (wind['speed'] ?? 0.0).toDouble(),
      pressure: (main['pressure'] ?? 0).toInt(),
      cloudiness: (clouds['all'] ?? 0).toInt(),
      precipitationProbability: json['pop']?.toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'dt': dateTime.millisecondsSinceEpoch ~/ 1000,
      'main': {
        'temp': temperature,
        'temp_min': tempMin,
        'temp_max': tempMax,
        'humidity': humidity,
        'pressure': pressure,
      },
      'weather': [
        {
          'main': condition,
          'description': description,
          'icon': iconCode,
        }
      ],
      'wind': {
        'speed': windSpeed,
      },
      'clouds': {
        'all': cloudiness,
      },
      if (precipitationProbability != null) 'pop': precipitationProbability,
    };
  }

  factory ForecastItemModel.fromEntity(ForecastItem item) {
    return ForecastItemModel(
      dateTime: item.dateTime,
      temperature: item.temperature,
      tempMin: item.tempMin,
      tempMax: item.tempMax,
      condition: item.condition,
      description: item.description,
      iconCode: item.iconCode,
      humidity: item.humidity,
      windSpeed: item.windSpeed,
      pressure: item.pressure,
      cloudiness: item.cloudiness,
      precipitationProbability: item.precipitationProbability,
    );
  }

  ForecastItem toEntity() {
    return ForecastItem(
      dateTime: dateTime,
      temperature: temperature,
      tempMin: tempMin,
      tempMax: tempMax,
      condition: condition,
      description: description,
      iconCode: iconCode,
      humidity: humidity,
      windSpeed: windSpeed,
      pressure: pressure,
      cloudiness: cloudiness,
      precipitationProbability: precipitationProbability,
    );
  }

  @override
  ForecastItemModel copyWith({
    DateTime? dateTime,
    double? temperature,
    double? tempMin,
    double? tempMax,
    String? condition,
    String? description,
    String? iconCode,
    int? humidity,
    double? windSpeed,
    int? pressure,
    int? cloudiness,
    double? precipitationProbability,
  }) {
    return ForecastItemModel(
      dateTime: dateTime ?? this.dateTime,
      temperature: temperature ?? this.temperature,
      tempMin: tempMin ?? this.tempMin,
      tempMax: tempMax ?? this.tempMax,
      condition: condition ?? this.condition,
      description: description ?? this.description,
      iconCode: iconCode ?? this.iconCode,
      humidity: humidity ?? this.humidity,
      windSpeed: windSpeed ?? this.windSpeed,
      pressure: pressure ?? this.pressure,
      cloudiness: cloudiness ?? this.cloudiness,
      precipitationProbability: precipitationProbability ?? this.precipitationProbability,
    );
  }
}

class ForecastModel extends Forecast {
  const ForecastModel({
    required super.items,
    required super.lastUpdated,
  });

  factory ForecastModel.fromJson(Map<String, dynamic> json) {
    final list = json['list'] as List? ?? [];
    final items = list
        .map((item) => ForecastItemModel.fromJson(item as Map<String, dynamic>))
        .toList();

    return ForecastModel(
      items: items,
      lastUpdated: DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'list': items.map((item) => (item as ForecastItemModel).toJson()).toList(),
      'lastUpdated': lastUpdated.millisecondsSinceEpoch,
    };
  }

  factory ForecastModel.fromEntity(Forecast forecast) {
    return ForecastModel(
      items: forecast.items
          .map((item) => ForecastItemModel.fromEntity(item))
          .toList(),
      lastUpdated: forecast.lastUpdated,
    );
  }

  Forecast toEntity() {
    return Forecast(
      items: items.map((item) => (item as ForecastItemModel).toEntity()).toList(),
      lastUpdated: lastUpdated,
    );
  }

  @override
  ForecastModel copyWith({
    List<ForecastItem>? items,
    DateTime? lastUpdated,
  }) {
    return ForecastModel(
      items: items ?? this.items,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }
}
