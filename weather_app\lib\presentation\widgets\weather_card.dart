import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../domain/entities/weather.dart';
import '../../domain/entities/location.dart';
import '../providers/theme_provider.dart';
import '../../core/utils.dart';

class WeatherCard extends StatelessWidget {
  final Weather weather;
  final Location location;

  const WeatherCard({
    super.key,
    required this.weather,
    required this.location,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        final isDark = themeProvider.isDarkMode(context);
        final language = themeProvider.language;
        
        return Container(
          width: double.infinity,
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                themeProvider.getWeatherBackgroundColor(weather.condition, isDark).withOpacity(0.8),
                themeProvider.getWeatherBackgroundColor(weather.condition, isDark).withOpacity(0.6),
              ],
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 10,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: Column(
            children: [
              // Location and time
              _buildLocationAndTime(context, language),
              const SizedBox(height: 20),
              
              // Main weather info
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // Temperature and condition
                  Expanded(
                    flex: 2,
                    child: _buildTemperatureSection(context, themeProvider),
                  ),
                  
                  // Weather icon
                  Expanded(
                    flex: 1,
                    child: _buildWeatherIcon(context, themeProvider),
                  ),
                ],
              ),
              
              const SizedBox(height: 20),
              
              // Min/Max temperature
              _buildMinMaxTemperature(context, themeProvider),
              
              const SizedBox(height: 16),
              
              // Feels like and description
              _buildFeelsLikeAndDescription(context, themeProvider),
            ],
          ),
        );
      },
    );
  }

  Widget _buildLocationAndTime(BuildContext context, String language) {
    final locationName = location.cityName ?? 
        '${location.latitude.toStringAsFixed(2)}, ${location.longitude.toStringAsFixed(2)}';
    
    return Column(
      children: [
        Text(
          locationName,
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 4),
        Text(
          DateTimeUtils.formatDateTime(weather.dateTime, language),
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Colors.white.withOpacity(0.8),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildTemperatureSection(BuildContext context, ThemeProvider themeProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          WeatherUtils.formatTemperature(weather.temperature, themeProvider.temperatureUnit),
          style: Theme.of(context).textTheme.displayLarge?.copyWith(
            fontWeight: FontWeight.w300,
            color: Colors.white,
            fontSize: 72,
          ),
        ),
        Text(
          WeatherUtils.translateWeatherCondition(weather.condition, themeProvider.language),
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            color: Colors.white.withOpacity(0.9),
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildWeatherIcon(BuildContext context, ThemeProvider themeProvider) {
    return Container(
      width: 100,
      height: 100,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: Colors.white.withOpacity(0.2),
      ),
      child: Icon(
        _getWeatherIcon(weather.iconCode),
        size: 60,
        color: Colors.white,
      ),
    );
  }

  Widget _buildMinMaxTemperature(BuildContext context, ThemeProvider themeProvider) {
    final language = themeProvider.language;
    
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        _buildTemperatureItem(
          context,
          language == 'ar' ? 'الأعلى' : 'High',
          WeatherUtils.formatTemperature(weather.tempMax, themeProvider.temperatureUnit),
          Icons.keyboard_arrow_up,
        ),
        Container(
          width: 1,
          height: 30,
          color: Colors.white.withOpacity(0.3),
        ),
        _buildTemperatureItem(
          context,
          language == 'ar' ? 'الأدنى' : 'Low',
          WeatherUtils.formatTemperature(weather.tempMin, themeProvider.temperatureUnit),
          Icons.keyboard_arrow_down,
        ),
      ],
    );
  }

  Widget _buildTemperatureItem(BuildContext context, String label, String value, IconData icon) {
    return Column(
      children: [
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              color: Colors.white.withOpacity(0.8),
              size: 20,
            ),
            const SizedBox(width: 4),
            Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.white.withOpacity(0.8),
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildFeelsLikeAndDescription(BuildContext context, ThemeProvider themeProvider) {
    final language = themeProvider.language;
    
    return Column(
      children: [
        Text(
          '${language == 'ar' ? 'يشعر وكأنه' : 'Feels like'} ${WeatherUtils.formatTemperature(weather.feelsLike, themeProvider.temperatureUnit)}',
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            color: Colors.white.withOpacity(0.9),
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          WeatherUtils.translateWeatherCondition(weather.description, language),
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Colors.white.withOpacity(0.8),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  IconData _getWeatherIcon(String iconCode) {
    switch (iconCode) {
      case '01d':
      case '01n':
        return Icons.wb_sunny;
      case '02d':
      case '02n':
      case '03d':
      case '03n':
        return Icons.wb_cloudy;
      case '04d':
      case '04n':
        return Icons.cloud;
      case '09d':
      case '09n':
      case '10d':
      case '10n':
        return Icons.grain;
      case '11d':
      case '11n':
        return Icons.flash_on;
      case '13d':
      case '13n':
        return Icons.ac_unit;
      case '50d':
      case '50n':
        return Icons.blur_on;
      default:
        return Icons.wb_sunny;
    }
  }
}
