class AppConstants {
  // API Configuration
  static const String baseUrl = 'https://api.openweathermap.org/data/2.5';
  static const String iconBaseUrl = 'https://openweathermap.org/img/wn';
  static const String defaultApiKey = 'YOUR_API_KEY_HERE'; // User should replace this
  
  // Storage Keys
  static const String apiKeyStorageKey = 'weather_api_key';
  static const String savedLocationsKey = 'saved_locations';
  static const String temperatureUnitKey = 'temperature_unit';
  static const String languageKey = 'language';
  static const String themeKey = 'theme_mode';
  static const String notificationsEnabledKey = 'notifications_enabled';
  
  // Default Values
  static const String defaultLanguage = 'ar';
  static const String defaultTemperatureUnit = 'metric';
  static const bool defaultNotificationsEnabled = true;
  
  // Animation Durations
  static const Duration shortAnimationDuration = Duration(milliseconds: 300);
  static const Duration mediumAnimationDuration = Duration(milliseconds: 500);
  static const Duration longAnimationDuration = Duration(milliseconds: 800);
  
  // UI Constants
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double borderRadius = 12.0;
  static const double cardElevation = 4.0;
  
  // Weather Icons Mapping
  static const Map<String, String> weatherIconMap = {
    '01d': 'clear_day',
    '01n': 'clear_night',
    '02d': 'partly_cloudy_day',
    '02n': 'partly_cloudy_night',
    '03d': 'cloudy',
    '03n': 'cloudy',
    '04d': 'overcast',
    '04n': 'overcast',
    '09d': 'rain',
    '09n': 'rain',
    '10d': 'rain_day',
    '10n': 'rain_night',
    '11d': 'thunderstorm',
    '11n': 'thunderstorm',
    '13d': 'snow',
    '13n': 'snow',
    '50d': 'mist',
    '50n': 'mist',
  };
  
  // Supported Languages
  static const List<String> supportedLanguages = ['ar', 'en'];
  
  // Temperature Units
  static const List<String> temperatureUnits = ['metric', 'imperial'];
}
