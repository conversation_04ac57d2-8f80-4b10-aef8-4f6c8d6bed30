import 'package:intl/intl.dart';

class WeatherUtils {
  // Temperature conversion
  static double celsiusToFahrenheit(double celsius) {
    return (celsius * 9 / 5) + 32;
  }
  
  static double fahrenheitToCelsius(double fahrenheit) {
    return (fahrenheit - 32) * 5 / 9;
  }
  
  static String formatTemperature(double temperature, String unit) {
    if (unit == 'imperial') {
      return '${temperature.round()}°F';
    }
    return '${temperature.round()}°C';
  }
  
  // Wind speed conversion
  static double mpsToKmh(double mps) {
    return mps * 3.6;
  }
  
  static double mpsToMph(double mps) {
    return mps * 2.237;
  }
  
  static String formatWindSpeed(double speed, String unit) {
    if (unit == 'imperial') {
      return '${mpsToMph(speed).toStringAsFixed(1)} mph';
    }
    return '${mpsToKmh(speed).toStringAsFixed(1)} km/h';
  }
  
  // Pressure conversion
  static String formatPressure(double pressure) {
    return '${pressure.round()} hPa';
  }
  
  // Humidity formatting
  static String formatHumidity(int humidity) {
    return '$humidity%';
  }
  
  // Visibility formatting
  static String formatVisibility(double visibility, String unit) {
    if (unit == 'imperial') {
      return '${(visibility * 0.621371).toStringAsFixed(1)} mi';
    }
    return '${visibility.toStringAsFixed(1)} km';
  }
  
  // UV Index description
  static String getUvIndexDescription(double uvIndex, String languageCode) {
    if (languageCode == 'ar') {
      if (uvIndex <= 2) return 'منخفض';
      if (uvIndex <= 5) return 'متوسط';
      if (uvIndex <= 7) return 'عالي';
      if (uvIndex <= 10) return 'عالي جداً';
      return 'خطير';
    } else {
      if (uvIndex <= 2) return 'Low';
      if (uvIndex <= 5) return 'Moderate';
      if (uvIndex <= 7) return 'High';
      if (uvIndex <= 10) return 'Very High';
      return 'Extreme';
    }
  }
  
  // Weather condition translation
  static String translateWeatherCondition(String condition, String languageCode) {
    if (languageCode == 'ar') {
      final Map<String, String> translations = {
        'clear sky': 'سماء صافية',
        'few clouds': 'غيوم قليلة',
        'scattered clouds': 'غيوم متناثرة',
        'broken clouds': 'غيوم متكسرة',
        'shower rain': 'أمطار خفيفة',
        'rain': 'مطر',
        'thunderstorm': 'عاصفة رعدية',
        'snow': 'ثلج',
        'mist': 'ضباب',
        'overcast clouds': 'غيوم كثيفة',
        'light rain': 'مطر خفيف',
        'moderate rain': 'مطر متوسط',
        'heavy rain': 'مطر غزير',
      };
      return translations[condition.toLowerCase()] ?? condition;
    }
    return condition;
  }
}

class DateTimeUtils {
  // Format date and time
  static String formatDateTime(DateTime dateTime, String languageCode) {
    if (languageCode == 'ar') {
      return DateFormat('EEEE، d MMMM yyyy - h:mm a', 'ar').format(dateTime);
    }
    return DateFormat('EEEE, MMMM d, yyyy - h:mm a', 'en').format(dateTime);
  }
  
  static String formatTime(DateTime dateTime, String languageCode) {
    if (languageCode == 'ar') {
      return DateFormat('h:mm a', 'ar').format(dateTime);
    }
    return DateFormat('h:mm a', 'en').format(dateTime);
  }
  
  static String formatDate(DateTime dateTime, String languageCode) {
    if (languageCode == 'ar') {
      return DateFormat('EEEE، d MMMM', 'ar').format(dateTime);
    }
    return DateFormat('EEEE, MMMM d', 'en').format(dateTime);
  }
  
  static String formatShortDate(DateTime dateTime, String languageCode) {
    if (languageCode == 'ar') {
      return DateFormat('d/M', 'ar').format(dateTime);
    }
    return DateFormat('M/d', 'en').format(dateTime);
  }
  
  // Get day name
  static String getDayName(DateTime dateTime, String languageCode) {
    if (languageCode == 'ar') {
      return DateFormat('EEEE', 'ar').format(dateTime);
    }
    return DateFormat('EEEE', 'en').format(dateTime);
  }
  
  // Check if date is today
  static bool isToday(DateTime dateTime) {
    final now = DateTime.now();
    return dateTime.year == now.year &&
           dateTime.month == now.month &&
           dateTime.day == now.day;
  }
  
  // Check if date is tomorrow
  static bool isTomorrow(DateTime dateTime) {
    final tomorrow = DateTime.now().add(const Duration(days: 1));
    return dateTime.year == tomorrow.year &&
           dateTime.month == tomorrow.month &&
           dateTime.day == tomorrow.day;
  }
}

class ValidationUtils {
  // Validate API key format
  static bool isValidApiKey(String apiKey) {
    return apiKey.isNotEmpty && apiKey.length >= 32;
  }
  
  // Validate coordinates
  static bool isValidLatitude(double latitude) {
    return latitude >= -90 && latitude <= 90;
  }
  
  static bool isValidLongitude(double longitude) {
    return longitude >= -180 && longitude <= 180;
  }
  
  // Validate city name
  static bool isValidCityName(String cityName) {
    return cityName.trim().isNotEmpty && cityName.trim().length >= 2;
  }
}
