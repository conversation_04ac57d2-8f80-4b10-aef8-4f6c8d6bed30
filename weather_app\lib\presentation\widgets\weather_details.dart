import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../domain/entities/weather.dart';
import '../providers/theme_provider.dart';
import '../../core/utils.dart';

class WeatherDetails extends StatelessWidget {
  final Weather weather;

  const WeatherDetails({
    super.key,
    required this.weather,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        final language = themeProvider.language;
        
        return Container(
          width: double.infinity,
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                language == 'ar' ? 'تفاصيل الطقس' : 'Weather Details',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              
              GridView.count(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                crossAxisCount: 2,
                childAspectRatio: 2.5,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                children: [
                  _buildDetailItem(
                    context,
                    language == 'ar' ? 'الرطوبة' : 'Humidity',
                    WeatherUtils.formatHumidity(weather.humidity),
                    Icons.water_drop,
                    Colors.blue,
                  ),
                  _buildDetailItem(
                    context,
                    language == 'ar' ? 'الرياح' : 'Wind',
                    WeatherUtils.formatWindSpeed(weather.windSpeed, themeProvider.temperatureUnit),
                    Icons.air,
                    Colors.green,
                  ),
                  _buildDetailItem(
                    context,
                    language == 'ar' ? 'الضغط' : 'Pressure',
                    WeatherUtils.formatPressure(weather.pressure.toDouble()),
                    Icons.speed,
                    Colors.orange,
                  ),
                  _buildDetailItem(
                    context,
                    language == 'ar' ? 'الرؤية' : 'Visibility',
                    WeatherUtils.formatVisibility(weather.visibility, themeProvider.temperatureUnit),
                    Icons.visibility,
                    Colors.purple,
                  ),
                  if (weather.uvIndex != null)
                    _buildDetailItem(
                      context,
                      language == 'ar' ? 'الأشعة فوق البنفسجية' : 'UV Index',
                      '${weather.uvIndex!.toStringAsFixed(1)} (${WeatherUtils.getUvIndexDescription(weather.uvIndex!, language)})',
                      Icons.wb_sunny,
                      Colors.red,
                    ),
                  _buildDetailItem(
                    context,
                    language == 'ar' ? 'الغيوم' : 'Cloudiness',
                    '${weather.cloudiness}%',
                    Icons.cloud,
                    Colors.grey,
                  ),
                ],
              ),
              
              const SizedBox(height: 16),
              
              // Sunrise and sunset
              _buildSunriseSunset(context, language),
            ],
          ),
        );
      },
    );
  }

  Widget _buildDetailItem(
    BuildContext context,
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              Icon(
                icon,
                size: 16,
                color: color,
              ),
              const SizedBox(width: 6),
              Expanded(
                child: Text(
                  label,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: color.withOpacity(0.8),
                    fontWeight: FontWeight.w500,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildSunriseSunset(BuildContext context, String language) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
          colors: [
            Colors.orange.withOpacity(0.1),
            Colors.blue.withOpacity(0.1),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.orange.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildSunItem(
              context,
              language == 'ar' ? 'الشروق' : 'Sunrise',
              DateTimeUtils.formatTime(weather.sunrise, language),
              Icons.wb_sunny,
              Colors.orange,
            ),
          ),
          Container(
            width: 1,
            height: 40,
            color: Colors.grey.withOpacity(0.3),
          ),
          Expanded(
            child: _buildSunItem(
              context,
              language == 'ar' ? 'الغروب' : 'Sunset',
              DateTimeUtils.formatTime(weather.sunset, language),
              Icons.brightness_3,
              Colors.blue,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSunItem(
    BuildContext context,
    String label,
    String time,
    IconData icon,
    Color color,
  ) {
    return Column(
      children: [
        Icon(
          icon,
          color: color,
          size: 24,
        ),
        const SizedBox(height: 8),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: color.withOpacity(0.8),
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          time,
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }
}
