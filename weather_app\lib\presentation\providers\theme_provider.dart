import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../core/constants.dart';

enum AppThemeMode { light, dark, system }

class ThemeProvider extends ChangeNotifier {
  final SharedPreferences _prefs;
  
  ThemeProvider({required SharedPreferences prefs}) : _prefs = prefs {
    _loadThemeMode();
  }

  AppThemeMode _themeMode = AppThemeMode.system;
  String _language = AppConstants.defaultLanguage;
  String _temperatureUnit = AppConstants.defaultTemperatureUnit;
  bool _notificationsEnabled = AppConstants.defaultNotificationsEnabled;

  // Getters
  AppThemeMode get themeMode => _themeMode;
  String get language => _language;
  String get temperatureUnit => _temperatureUnit;
  bool get notificationsEnabled => _notificationsEnabled;

  // Get Flutter ThemeMode
  ThemeMode get flutterThemeMode {
    switch (_themeMode) {
      case AppThemeMode.light:
        return ThemeMode.light;
      case AppThemeMode.dark:
        return ThemeMode.dark;
      case AppThemeMode.system:
        return ThemeMode.system;
    }
  }

  // Check if current theme is dark
  bool isDarkMode(BuildContext context) {
    switch (_themeMode) {
      case AppThemeMode.light:
        return false;
      case AppThemeMode.dark:
        return true;
      case AppThemeMode.system:
        return MediaQuery.of(context).platformBrightness == Brightness.dark;
    }
  }

  // Load saved preferences
  void _loadThemeMode() {
    final savedTheme = _prefs.getString(AppConstants.themeKey);
    if (savedTheme != null) {
      _themeMode = AppThemeMode.values.firstWhere(
        (mode) => mode.toString() == savedTheme,
        orElse: () => AppThemeMode.system,
      );
    }

    _language = _prefs.getString(AppConstants.languageKey) ?? AppConstants.defaultLanguage;
    _temperatureUnit = _prefs.getString(AppConstants.temperatureUnitKey) ?? AppConstants.defaultTemperatureUnit;
    _notificationsEnabled = _prefs.getBool(AppConstants.notificationsEnabledKey) ?? AppConstants.defaultNotificationsEnabled;
  }

  // Set theme mode
  Future<void> setThemeMode(AppThemeMode mode) async {
    if (_themeMode != mode) {
      _themeMode = mode;
      await _prefs.setString(AppConstants.themeKey, mode.toString());
      notifyListeners();
    }
  }

  // Set language
  Future<void> setLanguage(String languageCode) async {
    if (_language != languageCode) {
      _language = languageCode;
      await _prefs.setString(AppConstants.languageKey, languageCode);
      notifyListeners();
    }
  }

  // Set temperature unit
  Future<void> setTemperatureUnit(String unit) async {
    if (_temperatureUnit != unit) {
      _temperatureUnit = unit;
      await _prefs.setString(AppConstants.temperatureUnitKey, unit);
      notifyListeners();
    }
  }

  // Set notifications enabled
  Future<void> setNotificationsEnabled(bool enabled) async {
    if (_notificationsEnabled != enabled) {
      _notificationsEnabled = enabled;
      await _prefs.setBool(AppConstants.notificationsEnabledKey, enabled);
      notifyListeners();
    }
  }

  // Get theme mode display name
  String getThemeModeDisplayName(AppThemeMode mode, String languageCode) {
    if (languageCode == 'ar') {
      switch (mode) {
        case AppThemeMode.light:
          return 'فاتح';
        case AppThemeMode.dark:
          return 'داكن';
        case AppThemeMode.system:
          return 'تلقائي';
      }
    } else {
      switch (mode) {
        case AppThemeMode.light:
          return 'Light';
        case AppThemeMode.dark:
          return 'Dark';
        case AppThemeMode.system:
          return 'System';
      }
    }
  }

  // Get language display name
  String getLanguageDisplayName(String languageCode) {
    switch (languageCode) {
      case 'ar':
        return 'العربية';
      case 'en':
        return 'English';
      default:
        return languageCode;
    }
  }

  // Get temperature unit display name
  String getTemperatureUnitDisplayName(String unit, String languageCode) {
    if (languageCode == 'ar') {
      switch (unit) {
        case 'metric':
          return 'مئوية (°C)';
        case 'imperial':
          return 'فهرنهايت (°F)';
        default:
          return unit;
      }
    } else {
      switch (unit) {
        case 'metric':
          return 'Celsius (°C)';
        case 'imperial':
          return 'Fahrenheit (°F)';
        default:
          return unit;
      }
    }
  }

  // Get weather background color based on condition and time
  Color getWeatherBackgroundColor(String? condition, bool isDark) {
    if (condition == null) {
      return isDark ? Colors.grey[900]! : Colors.blue[100]!;
    }

    final conditionLower = condition.toLowerCase();
    
    if (conditionLower.contains('clear') || conditionLower.contains('sunny')) {
      return isDark ? Colors.indigo[900]! : Colors.orange[100]!;
    } else if (conditionLower.contains('cloud')) {
      return isDark ? Colors.blueGrey[900]! : Colors.grey[200]!;
    } else if (conditionLower.contains('rain') || conditionLower.contains('drizzle')) {
      return isDark ? Colors.blue[900]! : Colors.blue[100]!;
    } else if (conditionLower.contains('thunder') || conditionLower.contains('storm')) {
      return isDark ? Colors.deepPurple[900]! : Colors.purple[100]!;
    } else if (conditionLower.contains('snow')) {
      return isDark ? Colors.cyan[900]! : Colors.cyan[50]!;
    } else if (conditionLower.contains('mist') || conditionLower.contains('fog')) {
      return isDark ? Colors.grey[800]! : Colors.grey[100]!;
    }
    
    return isDark ? Colors.grey[900]! : Colors.blue[100]!;
  }

  // Get weather icon color
  Color getWeatherIconColor(String? condition, bool isDark) {
    if (condition == null) {
      return isDark ? Colors.white : Colors.blue;
    }

    final conditionLower = condition.toLowerCase();
    
    if (conditionLower.contains('clear') || conditionLower.contains('sunny')) {
      return Colors.orange;
    } else if (conditionLower.contains('cloud')) {
      return isDark ? Colors.grey[300]! : Colors.grey[600]!;
    } else if (conditionLower.contains('rain') || conditionLower.contains('drizzle')) {
      return Colors.blue;
    } else if (conditionLower.contains('thunder') || conditionLower.contains('storm')) {
      return Colors.purple;
    } else if (conditionLower.contains('snow')) {
      return Colors.cyan;
    } else if (conditionLower.contains('mist') || conditionLower.contains('fog')) {
      return isDark ? Colors.grey[400]! : Colors.grey[500]!;
    }
    
    return isDark ? Colors.white : Colors.blue;
  }

  // Reset all settings to default
  Future<void> resetToDefaults() async {
    await setThemeMode(AppThemeMode.system);
    await setLanguage(AppConstants.defaultLanguage);
    await setTemperatureUnit(AppConstants.defaultTemperatureUnit);
    await setNotificationsEnabled(AppConstants.defaultNotificationsEnabled);
  }
}
