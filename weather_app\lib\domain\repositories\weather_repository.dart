import '../entities/weather.dart';
import '../entities/forecast.dart';
import '../entities/location.dart';

abstract class WeatherRepository {
  // Get current weather
  Future<Weather> getCurrentWeatherByCoordinates({
    required double latitude,
    required double longitude,
    String units = 'metric',
    String language = 'en',
  });

  Future<Weather> getCurrentWeatherByCity({
    required String cityName,
    String units = 'metric',
    String language = 'en',
  });

  // Get weather forecast
  Future<Forecast> getForecastByCoordinates({
    required double latitude,
    required double longitude,
    String units = 'metric',
    String language = 'en',
  });

  Future<Forecast> getForecastByCity({
    required String cityName,
    String units = 'metric',
    String language = 'en',
  });

  // Location services
  Future<List<Location>> searchCities(String query);
  Future<Location> getLocationByCoordinates({
    required double latitude,
    required double longitude,
  });

  // Cache management
  Future<void> clearCache();
  Future<bool> isCacheValid();
}
