import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import '../../core/constants.dart';
import '../../core/errors.dart';
import '../models/weather_model.dart';
import '../models/forecast_model.dart';
import '../models/location_model.dart';

class WeatherApiService {
  final http.Client _client;
  final String _baseUrl = AppConstants.baseUrl;
  
  WeatherApiService({http.Client? client}) : _client = client ?? http.Client();

  // Get current weather by coordinates
  Future<WeatherModel> getCurrentWeatherByCoordinates({
    required double latitude,
    required double longitude,
    required String apiKey,
    String units = 'metric',
    String language = 'en',
  }) async {
    try {
      final uri = Uri.parse('$_baseUrl/weather').replace(queryParameters: {
        'lat': latitude.toString(),
        'lon': longitude.toString(),
        'appid': apiKey,
        'units': units,
        'lang': language,
      });

      final response = await _client.get(uri).timeout(
        const Duration(seconds: 10),
        onTimeout: () => throw const TimeoutException('Request timeout'),
      );

      return _handleWeatherResponse(response);
    } on SocketException {
      throw const NetworkException('No internet connection');
    } on TimeoutException {
      throw const TimeoutException('Request timeout');
    } catch (e) {
      if (e is AppException) rethrow;
      throw ServerException('Failed to get current weather: $e');
    }
  }

  // Get current weather by city name
  Future<WeatherModel> getCurrentWeatherByCity({
    required String cityName,
    required String apiKey,
    String units = 'metric',
    String language = 'en',
  }) async {
    try {
      final uri = Uri.parse('$_baseUrl/weather').replace(queryParameters: {
        'q': cityName,
        'appid': apiKey,
        'units': units,
        'lang': language,
      });

      final response = await _client.get(uri).timeout(
        const Duration(seconds: 10),
        onTimeout: () => throw const TimeoutException('Request timeout'),
      );

      return _handleWeatherResponse(response);
    } on SocketException {
      throw const NetworkException('No internet connection');
    } on TimeoutException {
      throw const TimeoutException('Request timeout');
    } catch (e) {
      if (e is AppException) rethrow;
      throw ServerException('Failed to get current weather: $e');
    }
  }

  // Get weather forecast by coordinates
  Future<ForecastModel> getForecastByCoordinates({
    required double latitude,
    required double longitude,
    required String apiKey,
    String units = 'metric',
    String language = 'en',
  }) async {
    try {
      final uri = Uri.parse('$_baseUrl/forecast').replace(queryParameters: {
        'lat': latitude.toString(),
        'lon': longitude.toString(),
        'appid': apiKey,
        'units': units,
        'lang': language,
      });

      final response = await _client.get(uri).timeout(
        const Duration(seconds: 10),
        onTimeout: () => throw const TimeoutException('Request timeout'),
      );

      return _handleForecastResponse(response);
    } on SocketException {
      throw const NetworkException('No internet connection');
    } on TimeoutException {
      throw const TimeoutException('Request timeout');
    } catch (e) {
      if (e is AppException) rethrow;
      throw ServerException('Failed to get forecast: $e');
    }
  }

  // Get weather forecast by city name
  Future<ForecastModel> getForecastByCity({
    required String cityName,
    required String apiKey,
    String units = 'metric',
    String language = 'en',
  }) async {
    try {
      final uri = Uri.parse('$_baseUrl/forecast').replace(queryParameters: {
        'q': cityName,
        'appid': apiKey,
        'units': units,
        'lang': language,
      });

      final response = await _client.get(uri).timeout(
        const Duration(seconds: 10),
        onTimeout: () => throw const TimeoutException('Request timeout'),
      );

      return _handleForecastResponse(response);
    } on SocketException {
      throw const NetworkException('No internet connection');
    } on TimeoutException {
      throw const TimeoutException('Request timeout');
    } catch (e) {
      if (e is AppException) rethrow;
      throw ServerException('Failed to get forecast: $e');
    }
  }

  // Search cities by name (geocoding)
  Future<List<LocationModel>> searchCities({
    required String query,
    required String apiKey,
    int limit = 5,
  }) async {
    try {
      final uri = Uri.parse('http://api.openweathermap.org/geo/1.0/direct').replace(queryParameters: {
        'q': query,
        'limit': limit.toString(),
        'appid': apiKey,
      });

      final response = await _client.get(uri).timeout(
        const Duration(seconds: 10),
        onTimeout: () => throw const TimeoutException('Request timeout'),
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.map((item) => LocationModel.fromGeocodingJson(item)).toList();
      } else {
        throw _handleApiError(response);
      }
    } on SocketException {
      throw const NetworkException('No internet connection');
    } on TimeoutException {
      throw const TimeoutException('Request timeout');
    } catch (e) {
      if (e is AppException) rethrow;
      throw ServerException('Failed to search cities: $e');
    }
  }

  // Get location name by coordinates (reverse geocoding)
  Future<LocationModel> getLocationByCoordinates({
    required double latitude,
    required double longitude,
    required String apiKey,
  }) async {
    try {
      final uri = Uri.parse('http://api.openweathermap.org/geo/1.0/reverse').replace(queryParameters: {
        'lat': latitude.toString(),
        'lon': longitude.toString(),
        'limit': '1',
        'appid': apiKey,
      });

      final response = await _client.get(uri).timeout(
        const Duration(seconds: 10),
        onTimeout: () => throw const TimeoutException('Request timeout'),
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        if (data.isNotEmpty) {
          return LocationModel.fromGeocodingJson(data.first);
        } else {
          return LocationModel.currentLocation(
            latitude: latitude,
            longitude: longitude,
            cityName: 'Unknown Location',
          );
        }
      } else {
        throw _handleApiError(response);
      }
    } on SocketException {
      throw const NetworkException('No internet connection');
    } on TimeoutException {
      throw const TimeoutException('Request timeout');
    } catch (e) {
      if (e is AppException) rethrow;
      throw ServerException('Failed to get location: $e');
    }
  }

  // Handle weather API response
  WeatherModel _handleWeatherResponse(http.Response response) {
    if (response.statusCode == 200) {
      try {
        final Map<String, dynamic> data = json.decode(response.body);
        return WeatherModel.fromJson(data);
      } catch (e) {
        throw const ParsingException('Failed to parse weather data');
      }
    } else {
      throw _handleApiError(response);
    }
  }

  // Handle forecast API response
  ForecastModel _handleForecastResponse(http.Response response) {
    if (response.statusCode == 200) {
      try {
        final Map<String, dynamic> data = json.decode(response.body);
        return ForecastModel.fromJson(data);
      } catch (e) {
        throw const ParsingException('Failed to parse forecast data');
      }
    } else {
      throw _handleApiError(response);
    }
  }

  // Handle API errors
  AppException _handleApiError(http.Response response) {
    switch (response.statusCode) {
      case 401:
        return const ApiKeyException('Invalid API key', statusCode: 401);
      case 404:
        return const ApiException('Location not found', statusCode: 404);
      case 429:
        return const ApiException('API rate limit exceeded', statusCode: 429);
      case 500:
      case 502:
      case 503:
        return ApiException('Server error', statusCode: response.statusCode);
      default:
        return ApiException('API error: ${response.reasonPhrase}', statusCode: response.statusCode);
    }
  }

  void dispose() {
    _client.close();
  }
}
