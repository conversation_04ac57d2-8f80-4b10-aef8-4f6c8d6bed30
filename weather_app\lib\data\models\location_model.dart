import '../../domain/entities/location.dart';

class LocationModel extends Location {
  const LocationModel({
    required super.latitude,
    required super.longitude,
    super.cityName,
    super.countryName,
    super.countryCode,
    super.isCurrentLocation,
  });

  factory LocationModel.fromJson(Map<String, dynamic> json) {
    return LocationModel(
      latitude: (json['lat'] ?? json['latitude'] ?? 0.0).toDouble(),
      longitude: (json['lon'] ?? json['longitude'] ?? 0.0).toDouble(),
      cityName: json['name'] ?? json['city_name'] ?? json['cityName'],
      countryName: json['country'] ?? json['country_name'] ?? json['countryName'],
      countryCode: json['country_code'] ?? json['countryCode'],
      isCurrentLocation: json['is_current_location'] ?? json['isCurrentLocation'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'latitude': latitude,
      'longitude': longitude,
      'cityName': cityName,
      'countryName': countryName,
      'countryCode': countryCode,
      'isCurrentLocation': isCurrentLocation,
    };
  }

  factory LocationModel.fromEntity(Location location) {
    return LocationModel(
      latitude: location.latitude,
      longitude: location.longitude,
      cityName: location.cityName,
      countryName: location.countryName,
      countryCode: location.countryCode,
      isCurrentLocation: location.isCurrentLocation,
    );
  }

  Location toEntity() {
    return Location(
      latitude: latitude,
      longitude: longitude,
      cityName: cityName,
      countryName: countryName,
      countryCode: countryCode,
      isCurrentLocation: isCurrentLocation,
    );
  }

  // Factory for creating from OpenWeatherMap geocoding API response
  factory LocationModel.fromGeocodingJson(Map<String, dynamic> json) {
    return LocationModel(
      latitude: (json['lat'] ?? 0.0).toDouble(),
      longitude: (json['lon'] ?? 0.0).toDouble(),
      cityName: json['name'],
      countryName: json['country'],
      countryCode: json['country'],
      isCurrentLocation: false,
    );
  }

  // Factory for creating current location
  factory LocationModel.currentLocation({
    required double latitude,
    required double longitude,
    String? cityName,
    String? countryName,
    String? countryCode,
  }) {
    return LocationModel(
      latitude: latitude,
      longitude: longitude,
      cityName: cityName,
      countryName: countryName,
      countryCode: countryCode,
      isCurrentLocation: true,
    );
  }

  @override
  LocationModel copyWith({
    double? latitude,
    double? longitude,
    String? cityName,
    String? countryName,
    String? countryCode,
    bool? isCurrentLocation,
  }) {
    return LocationModel(
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      cityName: cityName ?? this.cityName,
      countryName: countryName ?? this.countryName,
      countryCode: countryCode ?? this.countryCode,
      isCurrentLocation: isCurrentLocation ?? this.isCurrentLocation,
    );
  }
}
