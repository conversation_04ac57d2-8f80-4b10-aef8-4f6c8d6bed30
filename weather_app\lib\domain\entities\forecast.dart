class ForecastItem {
  final DateTime dateTime;
  final double temperature;
  final double tempMin;
  final double tempMax;
  final String condition;
  final String description;
  final String iconCode;
  final int humidity;
  final double windSpeed;
  final int pressure;
  final int cloudiness;
  final double? precipitationProbability;

  const ForecastItem({
    required this.dateTime,
    required this.temperature,
    required this.tempMin,
    required this.tempMax,
    required this.condition,
    required this.description,
    required this.iconCode,
    required this.humidity,
    required this.windSpeed,
    required this.pressure,
    required this.cloudiness,
    this.precipitationProbability,
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ForecastItem &&
        other.dateTime == dateTime &&
        other.temperature == temperature &&
        other.tempMin == tempMin &&
        other.tempMax == tempMax &&
        other.condition == condition &&
        other.description == description &&
        other.iconCode == iconCode &&
        other.humidity == humidity &&
        other.windSpeed == windSpeed &&
        other.pressure == pressure &&
        other.cloudiness == cloudiness &&
        other.precipitationProbability == precipitationProbability;
  }

  @override
  int get hashCode {
    return dateTime.hashCode ^
        temperature.hashCode ^
        tempMin.hashCode ^
        tempMax.hashCode ^
        condition.hashCode ^
        description.hashCode ^
        iconCode.hashCode ^
        humidity.hashCode ^
        windSpeed.hashCode ^
        pressure.hashCode ^
        cloudiness.hashCode ^
        precipitationProbability.hashCode;
  }

  @override
  String toString() {
    return 'ForecastItem(dateTime: $dateTime, temperature: $temperature, condition: $condition)';
  }

  ForecastItem copyWith({
    DateTime? dateTime,
    double? temperature,
    double? tempMin,
    double? tempMax,
    String? condition,
    String? description,
    String? iconCode,
    int? humidity,
    double? windSpeed,
    int? pressure,
    int? cloudiness,
    double? precipitationProbability,
  }) {
    return ForecastItem(
      dateTime: dateTime ?? this.dateTime,
      temperature: temperature ?? this.temperature,
      tempMin: tempMin ?? this.tempMin,
      tempMax: tempMax ?? this.tempMax,
      condition: condition ?? this.condition,
      description: description ?? this.description,
      iconCode: iconCode ?? this.iconCode,
      humidity: humidity ?? this.humidity,
      windSpeed: windSpeed ?? this.windSpeed,
      pressure: pressure ?? this.pressure,
      cloudiness: cloudiness ?? this.cloudiness,
      precipitationProbability: precipitationProbability ?? this.precipitationProbability,
    );
  }
}

class Forecast {
  final List<ForecastItem> items;
  final DateTime lastUpdated;

  const Forecast({
    required this.items,
    required this.lastUpdated,
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Forecast &&
        other.items == items &&
        other.lastUpdated == lastUpdated;
  }

  @override
  int get hashCode {
    return items.hashCode ^ lastUpdated.hashCode;
  }

  @override
  String toString() {
    return 'Forecast(items: ${items.length}, lastUpdated: $lastUpdated)';
  }

  Forecast copyWith({
    List<ForecastItem>? items,
    DateTime? lastUpdated,
  }) {
    return Forecast(
      items: items ?? this.items,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  // Get forecast for specific day
  List<ForecastItem> getForecastForDay(DateTime day) {
    return items.where((item) {
      return item.dateTime.year == day.year &&
             item.dateTime.month == day.month &&
             item.dateTime.day == day.day;
    }).toList();
  }

  // Get daily forecast (one item per day)
  List<ForecastItem> getDailyForecast() {
    final Map<String, ForecastItem> dailyMap = {};
    
    for (final item in items) {
      final dayKey = '${item.dateTime.year}-${item.dateTime.month}-${item.dateTime.day}';
      if (!dailyMap.containsKey(dayKey)) {
        dailyMap[dayKey] = item;
      }
    }
    
    return dailyMap.values.toList()..sort((a, b) => a.dateTime.compareTo(b.dateTime));
  }
}
