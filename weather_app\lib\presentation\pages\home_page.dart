import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/weather_provider.dart';
import '../providers/location_provider.dart';
import '../providers/theme_provider.dart';
import '../widgets/weather_card.dart';
import '../widgets/forecast_list.dart';
import '../widgets/weather_details.dart';
import '../widgets/location_search.dart';
import '../widgets/error_widget.dart';
import '../widgets/loading_widget.dart';
import '../../core/utils.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _initializeData();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _animationController.forward();
  }

  Future<void> _initializeData() async {
    final locationProvider = context.read<LocationProvider>();
    final weatherProvider = context.read<WeatherProvider>();

    // Initialize location services
    await locationProvider.initializeLocationServices();

    // Try to get last known location first
    await locationProvider.getLastKnownLocation();

    // If we have permission, get current location
    if (locationProvider.isPermissionGranted) {
      await locationProvider.getCurrentLocation();
    }

    // If we have a location, get weather data
    if (locationProvider.currentLocation != null) {
      await weatherProvider.getWeatherForLocation(locationProvider.currentLocation!);
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Consumer3<WeatherProvider, LocationProvider, ThemeProvider>(
        builder: (context, weatherProvider, locationProvider, themeProvider, child) {
          return Container(
            decoration: BoxDecoration(
              gradient: _buildBackgroundGradient(
                weatherProvider.currentWeather?.condition,
                themeProvider.isDarkMode(context),
              ),
            ),
            child: SafeArea(
              child: RefreshIndicator(
                onRefresh: _onRefresh,
                child: CustomScrollView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  slivers: [
                    _buildAppBar(context, locationProvider, themeProvider),
                    SliverToBoxAdapter(
                      child: FadeTransition(
                        opacity: _fadeAnimation,
                        child: SlideTransition(
                          position: _slideAnimation,
                          child: _buildContent(
                            context,
                            weatherProvider,
                            locationProvider,
                            themeProvider,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
      floatingActionButton: _buildFloatingActionButton(context),
    );
  }

  Widget _buildAppBar(
    BuildContext context,
    LocationProvider locationProvider,
    ThemeProvider themeProvider,
  ) {
    return SliverAppBar(
      expandedHeight: 120,
      floating: true,
      pinned: true,
      backgroundColor: Colors.transparent,
      elevation: 0,
      flexibleSpace: FlexibleSpaceBar(
        title: Text(
          _getAppBarTitle(locationProvider, themeProvider.language),
          style: TextStyle(
            color: themeProvider.isDarkMode(context) ? Colors.white : Colors.black87,
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: true,
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.search),
          onPressed: () => _showLocationSearch(context),
        ),
        IconButton(
          icon: const Icon(Icons.settings),
          onPressed: () => _navigateToSettings(context),
        ),
      ],
    );
  }

  Widget _buildContent(
    BuildContext context,
    WeatherProvider weatherProvider,
    LocationProvider locationProvider,
    ThemeProvider themeProvider,
  ) {
    // Handle location permission
    if (locationProvider.locationState == LocationState.permissionDenied) {
      return _buildPermissionDeniedWidget(context, locationProvider, themeProvider);
    }

    // Handle location error
    if (locationProvider.locationState == LocationState.error) {
      return WeatherErrorWidget(
        error: locationProvider.locationError ?? 'Location error',
        onRetry: () => locationProvider.getCurrentLocation(),
      );
    }

    // Handle weather error
    if (weatherProvider.currentWeatherState == WeatherState.error) {
      return WeatherErrorWidget(
        error: weatherProvider.currentWeatherError ?? 'Weather error',
        onRetry: () => _onRefresh(),
      );
    }

    // Handle loading state
    if (weatherProvider.isLoading || locationProvider.isLoading) {
      return const WeatherLoadingWidget();
    }

    // Handle no weather data
    if (!weatherProvider.hasCurrentWeather) {
      return _buildNoDataWidget(context, themeProvider);
    }

    // Show weather data
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          // Current weather card
          WeatherCard(
            weather: weatherProvider.currentWeather!,
            location: locationProvider.currentLocation!,
          ),
          const SizedBox(height: 20),
          
          // Weather details
          WeatherDetails(
            weather: weatherProvider.currentWeather!,
          ),
          const SizedBox(height: 20),
          
          // Forecast list
          if (weatherProvider.hasForecast)
            ForecastList(
              forecast: weatherProvider.forecast!,
            ),
        ],
      ),
    );
  }

  Widget _buildPermissionDeniedWidget(
    BuildContext context,
    LocationProvider locationProvider,
    ThemeProvider themeProvider,
  ) {
    final isArabic = themeProvider.language == 'ar';
    
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.location_off,
              size: 80,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 24),
            Text(
              isArabic ? 'إذن الموقع مطلوب' : 'Location Permission Required',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Text(
              isArabic 
                ? 'يحتاج التطبيق إلى إذن الوصول للموقع لعرض بيانات الطقس الحالية'
                : 'This app needs location permission to show current weather data',
              style: Theme.of(context).textTheme.bodyLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            ElevatedButton.icon(
              onPressed: () => locationProvider.requestLocationPermission(),
              icon: const Icon(Icons.location_on),
              label: Text(isArabic ? 'منح الإذن' : 'Grant Permission'),
            ),
            const SizedBox(height: 16),
            TextButton(
              onPressed: () => _showLocationSearch(context),
              child: Text(isArabic ? 'البحث عن مدينة' : 'Search for a city'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNoDataWidget(BuildContext context, ThemeProvider themeProvider) {
    final isArabic = themeProvider.language == 'ar';
    
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.cloud_off,
              size: 80,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 24),
            Text(
              isArabic ? 'لا توجد بيانات طقس' : 'No Weather Data',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () => _showLocationSearch(context),
              icon: const Icon(Icons.search),
              label: Text(isArabic ? 'البحث عن مدينة' : 'Search for a city'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFloatingActionButton(BuildContext context) {
    return Consumer<LocationProvider>(
      builder: (context, locationProvider, child) {
        if (!locationProvider.isPermissionGranted) return const SizedBox.shrink();
        
        return FloatingActionButton(
          onPressed: () => _getCurrentLocation(context),
          child: const Icon(Icons.my_location),
        );
      },
    );
  }

  LinearGradient _buildBackgroundGradient(String? condition, bool isDark) {
    if (condition == null) {
      return LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        colors: isDark 
          ? [Colors.grey[900]!, Colors.grey[800]!]
          : [Colors.blue[100]!, Colors.blue[50]!],
      );
    }

    final conditionLower = condition.toLowerCase();
    
    if (conditionLower.contains('clear') || conditionLower.contains('sunny')) {
      return LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        colors: isDark 
          ? [Colors.indigo[900]!, Colors.indigo[800]!]
          : [Colors.orange[200]!, Colors.orange[100]!],
      );
    } else if (conditionLower.contains('rain')) {
      return LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        colors: isDark 
          ? [Colors.blue[900]!, Colors.blue[800]!]
          : [Colors.blue[200]!, Colors.blue[100]!],
      );
    }
    
    return LinearGradient(
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
      colors: isDark 
        ? [Colors.grey[900]!, Colors.grey[800]!]
        : [Colors.grey[200]!, Colors.grey[100]!],
    );
  }

  String _getAppBarTitle(LocationProvider locationProvider, String language) {
    if (locationProvider.currentLocation != null) {
      return locationProvider.getLocationDisplayName(locationProvider.currentLocation!);
    }
    return language == 'ar' ? 'طقس' : 'Weather';
  }

  Future<void> _onRefresh() async {
    final weatherProvider = context.read<WeatherProvider>();
    final locationProvider = context.read<LocationProvider>();

    if (locationProvider.currentLocation != null) {
      await weatherProvider.getWeatherForLocation(locationProvider.currentLocation!);
    } else {
      await locationProvider.getCurrentLocation();
      if (locationProvider.currentLocation != null) {
        await weatherProvider.getWeatherForLocation(locationProvider.currentLocation!);
      }
    }
  }

  Future<void> _getCurrentLocation(BuildContext context) async {
    final locationProvider = context.read<LocationProvider>();
    final weatherProvider = context.read<WeatherProvider>();

    await locationProvider.getCurrentLocation();
    if (locationProvider.currentLocation != null) {
      await weatherProvider.getWeatherForLocation(locationProvider.currentLocation!);
    }
  }

  void _showLocationSearch(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => const LocationSearch(),
    );
  }

  void _navigateToSettings(BuildContext context) {
    Navigator.pushNamed(context, '/settings');
  }
}
