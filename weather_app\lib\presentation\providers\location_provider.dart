import 'package:flutter/foundation.dart';
import '../../domain/entities/location.dart';
import '../../domain/repositories/location_repository.dart';
import '../../core/errors.dart';

enum LocationState { initial, loading, loaded, error, permissionDenied }

class LocationProvider extends ChangeNotifier {
  final LocationRepository _locationRepository;

  LocationProvider({required LocationRepository locationRepository})
      : _locationRepository = locationRepository;

  // Current location state
  LocationState _locationState = LocationState.initial;
  Location? _currentLocation;
  String? _locationError;

  // Saved locations
  List<Location> _savedLocations = [];
  bool _savedLocationsLoaded = false;

  // Permission state
  bool _isLocationServiceEnabled = false;
  bool _isPermissionGranted = false;

  // Getters
  LocationState get locationState => _locationState;
  Location? get currentLocation => _currentLocation;
  String? get locationError => _locationError;
  List<Location> get savedLocations => _savedLocations;
  bool get savedLocationsLoaded => _savedLocationsLoaded;
  bool get isLocationServiceEnabled => _isLocationServiceEnabled;
  bool get isPermissionGranted => _isPermissionGranted;
  bool get isLoading => _locationState == LocationState.loading;
  bool get hasCurrentLocation => _currentLocation != null;

  // Initialize location services
  Future<void> initializeLocationServices() async {
    try {
      _isLocationServiceEnabled = await _locationRepository.isLocationServiceEnabled();
      _isPermissionGranted = await _locationRepository.isPermissionGranted();
      notifyListeners();
    } catch (e) {
      debugPrint('Failed to initialize location services: $e');
    }
  }

  // Get current location
  Future<void> getCurrentLocation() async {
    _setLocationState(LocationState.loading);

    try {
      // Check if location service is enabled
      _isLocationServiceEnabled = await _locationRepository.isLocationServiceEnabled();
      if (!_isLocationServiceEnabled) {
        _locationError = 'Location services are disabled';
        _setLocationState(LocationState.error);
        return;
      }

      // Check permission
      _isPermissionGranted = await _locationRepository.isPermissionGranted();
      if (!_isPermissionGranted) {
        _locationError = 'Location permission not granted';
        _setLocationState(LocationState.permissionDenied);
        return;
      }

      // Get current location
      final location = await _locationRepository.getCurrentLocation();
      _currentLocation = location;
      _locationError = null;
      _setLocationState(LocationState.loaded);
    } catch (e) {
      _locationError = ErrorHandler.getLocalizedErrorMessage(
        e is Exception ? e : Exception(e.toString()),
        'en', // Default to English for now
      );
      
      if (e is LocationPermissionException) {
        _setLocationState(LocationState.permissionDenied);
      } else {
        _setLocationState(LocationState.error);
      }
    }
  }

  // Get last known location
  Future<void> getLastKnownLocation() async {
    try {
      final location = await _locationRepository.getLastKnownLocation();
      if (location != null) {
        _currentLocation = location;
        _setLocationState(LocationState.loaded);
      }
    } catch (e) {
      debugPrint('Failed to get last known location: $e');
    }
  }

  // Request location permission
  Future<bool> requestLocationPermission() async {
    try {
      final granted = await _locationRepository.requestPermission();
      _isPermissionGranted = granted;
      notifyListeners();
      
      if (granted) {
        await getCurrentLocation();
      }
      
      return granted;
    } catch (e) {
      _locationError = ErrorHandler.getLocalizedErrorMessage(
        e is Exception ? e : Exception(e.toString()),
        'en',
      );
      _setLocationState(LocationState.error);
      return false;
    }
  }

  // Open location settings
  Future<void> openLocationSettings() async {
    try {
      await _locationRepository.openLocationSettings();
    } catch (e) {
      debugPrint('Failed to open location settings: $e');
    }
  }

  // Open app settings
  Future<void> openAppSettings() async {
    try {
      await _locationRepository.openAppSettings();
    } catch (e) {
      debugPrint('Failed to open app settings: $e');
    }
  }

  // Load saved locations
  Future<void> loadSavedLocations() async {
    try {
      _savedLocations = await _locationRepository.getSavedLocations();
      _savedLocationsLoaded = true;
      notifyListeners();
    } catch (e) {
      debugPrint('Failed to load saved locations: $e');
      _savedLocationsLoaded = true;
      notifyListeners();
    }
  }

  // Save location
  Future<void> saveLocation(Location location) async {
    try {
      await _locationRepository.saveLocation(location);
      await loadSavedLocations(); // Reload the list
    } catch (e) {
      throw ErrorHandler.getLocalizedErrorMessage(
        e is Exception ? e : Exception(e.toString()),
        'en',
      );
    }
  }

  // Remove saved location
  Future<void> removeSavedLocation(Location location) async {
    try {
      await _locationRepository.removeLocation(location);
      await loadSavedLocations(); // Reload the list
    } catch (e) {
      throw ErrorHandler.getLocalizedErrorMessage(
        e is Exception ? e : Exception(e.toString()),
        'en',
      );
    }
  }

  // Clear all saved locations
  Future<void> clearSavedLocations() async {
    try {
      await _locationRepository.clearSavedLocations();
      _savedLocations.clear();
      notifyListeners();
    } catch (e) {
      throw ErrorHandler.getLocalizedErrorMessage(
        e is Exception ? e : Exception(e.toString()),
        'en',
      );
    }
  }

  // Check if location is saved
  bool isLocationSaved(Location location) {
    return _savedLocations.any((saved) =>
        saved.latitude == location.latitude &&
        saved.longitude == location.longitude);
  }

  // Calculate distance between two locations
  double calculateDistance(Location from, Location to) {
    return _locationRepository.calculateDistance(from, to);
  }

  // Set current location manually (for city search)
  void setCurrentLocation(Location location) {
    _currentLocation = location;
    _setLocationState(LocationState.loaded);
  }

  // Clear current location
  void clearCurrentLocation() {
    _currentLocation = null;
    _setLocationState(LocationState.initial);
  }

  // Clear error
  void clearError() {
    _locationError = null;
    notifyListeners();
  }

  // Refresh location data
  Future<void> refreshLocation() async {
    if (_isPermissionGranted && _isLocationServiceEnabled) {
      await getCurrentLocation();
    } else {
      await initializeLocationServices();
    }
  }

  // Private methods
  void _setLocationState(LocationState state) {
    _locationState = state;
    notifyListeners();
  }

  // Get location stream (for real-time updates)
  Stream<Location> getLocationStream() {
    return _locationRepository.getLocationStream();
  }

  // Start location tracking
  void startLocationTracking() {
    getLocationStream().listen(
      (location) {
        _currentLocation = location;
        _setLocationState(LocationState.loaded);
      },
      onError: (error) {
        _locationError = ErrorHandler.getLocalizedErrorMessage(
          error is Exception ? error : Exception(error.toString()),
          'en',
        );
        _setLocationState(LocationState.error);
      },
    );
  }

  // Get formatted location name
  String getLocationDisplayName(Location location) {
    if (location.cityName != null && location.countryName != null) {
      return '${location.cityName}, ${location.countryName}';
    } else if (location.cityName != null) {
      return location.cityName!;
    } else if (location.countryName != null) {
      return location.countryName!;
    } else {
      return '${location.latitude.toStringAsFixed(2)}, ${location.longitude.toStringAsFixed(2)}';
    }
  }
}
