import '../entities/location.dart';

abstract class LocationRepository {
  // Current location
  Future<Location> getCurrentLocation();
  Future<Location?> getLastKnownLocation();
  Stream<Location> getLocationStream();

  // Permission management
  Future<bool> isLocationServiceEnabled();
  Future<bool> isPermissionGranted();
  Future<bool> requestPermission();
  Future<void> openLocationSettings();
  Future<void> openAppSettings();

  // Saved locations
  Future<List<Location>> getSavedLocations();
  Future<void> saveLocation(Location location);
  Future<void> removeLocation(Location location);
  Future<void> clearSavedLocations();

  // Utilities
  double calculateDistance(Location from, Location to);
  bool isValidCoordinates(double latitude, double longitude);
}
