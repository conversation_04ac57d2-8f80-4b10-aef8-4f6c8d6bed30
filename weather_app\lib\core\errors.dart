// Base Exception Class
abstract class AppException implements Exception {
  final String message;
  final String? code;
  
  const AppException(this.message, {this.code});
  
  @override
  String toString() => 'AppException: $message${code != null ? ' (Code: $code)' : ''}';
}

// Network Exceptions
class NetworkException extends AppException {
  const NetworkException(super.message, {super.code});
}

class ServerException extends AppException {
  const ServerException(super.message, {super.code});
}

class TimeoutException extends AppException {
  const TimeoutException(super.message, {super.code});
}

// Location Exceptions
class LocationException extends AppException {
  const LocationException(super.message, {super.code});
}

class LocationPermissionException extends LocationException {
  const LocationPermissionException(super.message, {super.code});
}

class LocationServiceException extends LocationException {
  const LocationServiceException(super.message, {super.code});
}

// API Exceptions
class ApiException extends AppException {
  final int? statusCode;
  
  const ApiException(super.message, {this.statusCode, super.code});
  
  @override
  String toString() => 'ApiException: $message${statusCode != null ? ' (Status: $statusCode)' : ''}${code != null ? ' (Code: $code)' : ''}';
}

class ApiKeyException extends ApiException {
  const ApiKeyException(super.message, {super.statusCode, super.code});
}

// Cache Exceptions
class CacheException extends AppException {
  const CacheException(super.message, {super.code});
}

// Parsing Exceptions
class ParsingException extends AppException {
  const ParsingException(super.message, {super.code});
}

// Utility class for error handling
class ErrorHandler {
  static String getErrorMessage(Exception exception) {
    if (exception is AppException) {
      return exception.message;
    }
    return 'An unexpected error occurred';
  }
  
  static String getLocalizedErrorMessage(Exception exception, String languageCode) {
    if (exception is NetworkException) {
      return languageCode == 'ar' 
          ? 'خطأ في الاتصال بالإنترنت'
          : 'Network connection error';
    }
    
    if (exception is LocationPermissionException) {
      return languageCode == 'ar'
          ? 'يرجى السماح بالوصول للموقع'
          : 'Please allow location access';
    }
    
    if (exception is ApiKeyException) {
      return languageCode == 'ar'
          ? 'مفتاح API غير صحيح'
          : 'Invalid API key';
    }
    
    if (exception is ServerException) {
      return languageCode == 'ar'
          ? 'خطأ في الخادم'
          : 'Server error';
    }
    
    return languageCode == 'ar'
        ? 'حدث خطأ غير متوقع'
        : 'An unexpected error occurred';
  }
}
