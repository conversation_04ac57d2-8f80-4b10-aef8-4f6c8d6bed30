import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/theme_provider.dart';

class WeatherErrorWidget extends StatelessWidget {
  final String error;
  final VoidCallback? onRetry;
  final IconData? icon;

  const WeatherErrorWidget({
    super.key,
    required this.error,
    this.onRetry,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        final isArabic = themeProvider.language == 'ar';
        
        return Center(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Error icon
                Container(
                  width: 100,
                  height: 100,
                  decoration: BoxDecoration(
                    color: Colors.red.withOpacity(0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    icon ?? Icons.error_outline,
                    size: 50,
                    color: Colors.red[400],
                  ),
                ),
                const SizedBox(height: 24),
                
                // Error title
                Text(
                  isArabic ? 'حدث خطأ' : 'Something went wrong',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.red[700],
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                
                // Error message
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.red.withOpacity(0.05),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Colors.red.withOpacity(0.2),
                      width: 1,
                    ),
                  ),
                  child: Text(
                    error,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.red[600],
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                
                if (onRetry != null) ...[
                  const SizedBox(height: 24),
                  
                  // Retry button
                  ElevatedButton.icon(
                    onPressed: onRetry,
                    icon: const Icon(Icons.refresh),
                    label: Text(isArabic ? 'إعادة المحاولة' : 'Try Again'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red[600],
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 24,
                        vertical: 12,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }
}

class NetworkErrorWidget extends StatelessWidget {
  final VoidCallback? onRetry;

  const NetworkErrorWidget({
    super.key,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        final isArabic = themeProvider.language == 'ar';
        
        return WeatherErrorWidget(
          error: isArabic 
            ? 'لا يوجد اتصال بالإنترنت. يرجى التحقق من اتصالك والمحاولة مرة أخرى.'
            : 'No internet connection. Please check your connection and try again.',
          onRetry: onRetry,
          icon: Icons.wifi_off,
        );
      },
    );
  }
}

class ApiKeyErrorWidget extends StatelessWidget {
  final VoidCallback? onSettings;

  const ApiKeyErrorWidget({
    super.key,
    this.onSettings,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        final isArabic = themeProvider.language == 'ar';
        
        return Center(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // API key icon
                Container(
                  width: 100,
                  height: 100,
                  decoration: BoxDecoration(
                    color: Colors.orange.withOpacity(0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.key,
                    size: 50,
                    color: Colors.orange[600],
                  ),
                ),
                const SizedBox(height: 24),
                
                // Title
                Text(
                  isArabic ? 'مفتاح API مطلوب' : 'API Key Required',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.orange[700],
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                
                // Message
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.orange.withOpacity(0.05),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Colors.orange.withOpacity(0.2),
                      width: 1,
                    ),
                  ),
                  child: Text(
                    isArabic 
                      ? 'يحتاج التطبيق إلى مفتاح API من OpenWeatherMap للعمل. يرجى إضافة مفتاح API في الإعدادات.'
                      : 'This app needs an OpenWeatherMap API key to work. Please add your API key in settings.',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.orange[600],
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                const SizedBox(height: 24),
                
                // Settings button
                ElevatedButton.icon(
                  onPressed: onSettings ?? () => Navigator.pushNamed(context, '/settings'),
                  icon: const Icon(Icons.settings),
                  label: Text(isArabic ? 'فتح الإعدادات' : 'Open Settings'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange[600],
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 12,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                
                // Help text
                TextButton(
                  onPressed: () => _showApiKeyHelp(context, isArabic),
                  child: Text(
                    isArabic ? 'كيفية الحصول على مفتاح API؟' : 'How to get an API key?',
                    style: TextStyle(
                      color: Colors.orange[600],
                      decoration: TextDecoration.underline,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _showApiKeyHelp(BuildContext context, bool isArabic) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(isArabic ? 'كيفية الحصول على مفتاح API' : 'How to get an API key'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                isArabic 
                  ? '1. اذهب إلى openweathermap.org\n2. أنشئ حساباً مجانياً\n3. اذهب إلى قسم API keys\n4. انسخ مفتاح API\n5. الصقه في إعدادات التطبيق'
                  : '1. Go to openweathermap.org\n2. Create a free account\n3. Go to API keys section\n4. Copy your API key\n5. Paste it in app settings',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(isArabic ? 'حسناً' : 'OK'),
          ),
        ],
      ),
    );
  }
}
