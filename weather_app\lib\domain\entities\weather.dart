class Weather {
  final double temperature;
  final double feelsLike;
  final double tempMin;
  final double tempMax;
  final int pressure;
  final int humidity;
  final double windSpeed;
  final int windDirection;
  final double? windGust;
  final int visibility;
  final double? uvIndex;
  final int cloudiness;
  final String condition;
  final String description;
  final String iconCode;
  final DateTime dateTime;
  final DateTime sunrise;
  final DateTime sunset;

  const Weather({
    required this.temperature,
    required this.feelsLike,
    required this.tempMin,
    required this.tempMax,
    required this.pressure,
    required this.humidity,
    required this.windSpeed,
    required this.windDirection,
    this.windGust,
    required this.visibility,
    this.uvIndex,
    required this.cloudiness,
    required this.condition,
    required this.description,
    required this.iconCode,
    required this.dateTime,
    required this.sunrise,
    required this.sunset,
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Weather &&
        other.temperature == temperature &&
        other.feelsLike == feelsLike &&
        other.tempMin == tempMin &&
        other.tempMax == tempMax &&
        other.pressure == pressure &&
        other.humidity == humidity &&
        other.windSpeed == windSpeed &&
        other.windDirection == windDirection &&
        other.windGust == windGust &&
        other.visibility == visibility &&
        other.uvIndex == uvIndex &&
        other.cloudiness == cloudiness &&
        other.condition == condition &&
        other.description == description &&
        other.iconCode == iconCode &&
        other.dateTime == dateTime &&
        other.sunrise == sunrise &&
        other.sunset == sunset;
  }

  @override
  int get hashCode {
    return temperature.hashCode ^
        feelsLike.hashCode ^
        tempMin.hashCode ^
        tempMax.hashCode ^
        pressure.hashCode ^
        humidity.hashCode ^
        windSpeed.hashCode ^
        windDirection.hashCode ^
        windGust.hashCode ^
        visibility.hashCode ^
        uvIndex.hashCode ^
        cloudiness.hashCode ^
        condition.hashCode ^
        description.hashCode ^
        iconCode.hashCode ^
        dateTime.hashCode ^
        sunrise.hashCode ^
        sunset.hashCode;
  }

  @override
  String toString() {
    return 'Weather(temperature: $temperature, condition: $condition, description: $description, dateTime: $dateTime)';
  }

  Weather copyWith({
    double? temperature,
    double? feelsLike,
    double? tempMin,
    double? tempMax,
    int? pressure,
    int? humidity,
    double? windSpeed,
    int? windDirection,
    double? windGust,
    int? visibility,
    double? uvIndex,
    int? cloudiness,
    String? condition,
    String? description,
    String? iconCode,
    DateTime? dateTime,
    DateTime? sunrise,
    DateTime? sunset,
  }) {
    return Weather(
      temperature: temperature ?? this.temperature,
      feelsLike: feelsLike ?? this.feelsLike,
      tempMin: tempMin ?? this.tempMin,
      tempMax: tempMax ?? this.tempMax,
      pressure: pressure ?? this.pressure,
      humidity: humidity ?? this.humidity,
      windSpeed: windSpeed ?? this.windSpeed,
      windDirection: windDirection ?? this.windDirection,
      windGust: windGust ?? this.windGust,
      visibility: visibility ?? this.visibility,
      uvIndex: uvIndex ?? this.uvIndex,
      cloudiness: cloudiness ?? this.cloudiness,
      condition: condition ?? this.condition,
      description: description ?? this.description,
      iconCode: iconCode ?? this.iconCode,
      dateTime: dateTime ?? this.dateTime,
      sunrise: sunrise ?? this.sunrise,
      sunset: sunset ?? this.sunset,
    );
  }
}
