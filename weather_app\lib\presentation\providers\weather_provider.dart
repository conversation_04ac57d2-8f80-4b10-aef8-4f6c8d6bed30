import 'package:flutter/foundation.dart';
import '../../domain/entities/weather.dart';
import '../../domain/entities/forecast.dart';
import '../../domain/entities/location.dart';
import '../../domain/repositories/weather_repository.dart';
import '../../core/errors.dart';

enum WeatherState { initial, loading, loaded, error }

class WeatherProvider extends ChangeNotifier {
  final WeatherRepository _weatherRepository;

  WeatherProvider({required WeatherRepository weatherRepository})
      : _weatherRepository = weatherRepository;

  // Current weather state
  WeatherState _currentWeatherState = WeatherState.initial;
  Weather? _currentWeather;
  String? _currentWeatherError;

  // Forecast state
  WeatherState _forecastState = WeatherState.initial;
  Forecast? _forecast;
  String? _forecastError;

  // Current location
  Location? _currentLocation;

  // Settings
  String _temperatureUnit = 'metric';
  String _language = 'en';

  // Getters
  WeatherState get currentWeatherState => _currentWeatherState;
  Weather? get currentWeather => _currentWeather;
  String? get currentWeatherError => _currentWeatherError;

  WeatherState get forecastState => _forecastState;
  Forecast? get forecast => _forecast;
  String? get forecastError => _forecastError;

  Location? get currentLocation => _currentLocation;
  String get temperatureUnit => _temperatureUnit;
  String get language => _language;

  bool get isLoading => _currentWeatherState == WeatherState.loading ||
                      _forecastState == WeatherState.loading;

  bool get hasCurrentWeather => _currentWeather != null;
  bool get hasForecast => _forecast != null;

  // Get current weather by coordinates
  Future<void> getCurrentWeatherByCoordinates({
    required double latitude,
    required double longitude,
  }) async {
    _setCurrentWeatherState(WeatherState.loading);

    try {
      final weather = await _weatherRepository.getCurrentWeatherByCoordinates(
        latitude: latitude,
        longitude: longitude,
        units: _temperatureUnit,
        language: _language,
      );

      _currentWeather = weather;
      _currentWeatherError = null;
      _setCurrentWeatherState(WeatherState.loaded);
    } catch (e) {
      _currentWeatherError = ErrorHandler.getLocalizedErrorMessage(
        e is Exception ? e : Exception(e.toString()),
        _language,
      );
      _setCurrentWeatherState(WeatherState.error);
    }
  }

  // Get current weather by city
  Future<void> getCurrentWeatherByCity(String cityName) async {
    _setCurrentWeatherState(WeatherState.loading);

    try {
      final weather = await _weatherRepository.getCurrentWeatherByCity(
        cityName: cityName,
        units: _temperatureUnit,
        language: _language,
      );

      _currentWeather = weather;
      _currentWeatherError = null;
      _setCurrentWeatherState(WeatherState.loaded);
    } catch (e) {
      _currentWeatherError = ErrorHandler.getLocalizedErrorMessage(
        e is Exception ? e : Exception(e.toString()),
        _language,
      );
      _setCurrentWeatherState(WeatherState.error);
    }
  }

  // Get forecast by coordinates
  Future<void> getForecastByCoordinates({
    required double latitude,
    required double longitude,
  }) async {
    _setForecastState(WeatherState.loading);

    try {
      final forecast = await _weatherRepository.getForecastByCoordinates(
        latitude: latitude,
        longitude: longitude,
        units: _temperatureUnit,
        language: _language,
      );

      _forecast = forecast;
      _forecastError = null;
      _setForecastState(WeatherState.loaded);
    } catch (e) {
      _forecastError = ErrorHandler.getLocalizedErrorMessage(
        e is Exception ? e : Exception(e.toString()),
        _language,
      );
      _setForecastState(WeatherState.error);
    }
  }

  // Get forecast by city
  Future<void> getForecastByCity(String cityName) async {
    _setForecastState(WeatherState.loading);

    try {
      final forecast = await _weatherRepository.getForecastByCity(
        cityName: cityName,
        units: _temperatureUnit,
        language: _language,
      );

      _forecast = forecast;
      _forecastError = null;
      _setForecastState(WeatherState.loaded);
    } catch (e) {
      _forecastError = ErrorHandler.getLocalizedErrorMessage(
        e is Exception ? e : Exception(e.toString()),
        _language,
      );
      _setForecastState(WeatherState.error);
    }
  }

  // Get weather data for location
  Future<void> getWeatherForLocation(Location location) async {
    _currentLocation = location;
    
    // Get both current weather and forecast
    await Future.wait([
      getCurrentWeatherByCoordinates(
        latitude: location.latitude,
        longitude: location.longitude,
      ),
      getForecastByCoordinates(
        latitude: location.latitude,
        longitude: location.longitude,
      ),
    ]);
  }

  // Search cities
  Future<List<Location>> searchCities(String query) async {
    try {
      return await _weatherRepository.searchCities(query);
    } catch (e) {
      throw ErrorHandler.getLocalizedErrorMessage(
        e is Exception ? e : Exception(e.toString()),
        _language,
      );
    }
  }

  // Refresh weather data
  Future<void> refreshWeatherData() async {
    if (_currentLocation != null) {
      await getWeatherForLocation(_currentLocation!);
    }
  }

  // Update settings
  void updateTemperatureUnit(String unit) {
    if (_temperatureUnit != unit) {
      _temperatureUnit = unit;
      notifyListeners();
      // Refresh data with new unit
      if (_currentLocation != null) {
        refreshWeatherData();
      }
    }
  }

  void updateLanguage(String language) {
    if (_language != language) {
      _language = language;
      notifyListeners();
      // Refresh data with new language
      if (_currentLocation != null) {
        refreshWeatherData();
      }
    }
  }

  // Clear data
  void clearWeatherData() {
    _currentWeather = null;
    _forecast = null;
    _currentLocation = null;
    _currentWeatherError = null;
    _forecastError = null;
    _setCurrentWeatherState(WeatherState.initial);
    _setForecastState(WeatherState.initial);
  }

  // Clear errors
  void clearErrors() {
    _currentWeatherError = null;
    _forecastError = null;
    notifyListeners();
  }

  // Private methods
  void _setCurrentWeatherState(WeatherState state) {
    _currentWeatherState = state;
    notifyListeners();
  }

  void _setForecastState(WeatherState state) {
    _forecastState = state;
    notifyListeners();
  }

  // Get daily forecast items
  List<ForecastItem> getDailyForecast() {
    return _forecast?.getDailyForecast() ?? [];
  }

  // Get forecast for specific day
  List<ForecastItem> getForecastForDay(DateTime day) {
    return _forecast?.getForecastForDay(day) ?? [];
  }

  // Check if data is stale (older than 10 minutes)
  bool get isDataStale {
    if (_currentWeather == null) return true;
    final now = DateTime.now();
    final difference = now.difference(_currentWeather!.dateTime);
    return difference.inMinutes > 10;
  }
}
