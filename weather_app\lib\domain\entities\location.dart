class Location {
  final double latitude;
  final double longitude;
  final String? cityName;
  final String? countryName;
  final String? countryCode;
  final bool isCurrentLocation;

  const Location({
    required this.latitude,
    required this.longitude,
    this.cityName,
    this.countryName,
    this.countryCode,
    this.isCurrentLocation = false,
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Location &&
        other.latitude == latitude &&
        other.longitude == longitude &&
        other.cityName == cityName &&
        other.countryName == countryName &&
        other.countryCode == countryCode &&
        other.isCurrentLocation == isCurrentLocation;
  }

  @override
  int get hashCode {
    return latitude.hashCode ^
        longitude.hashCode ^
        cityName.hashCode ^
        countryName.hashCode ^
        countryCode.hashCode ^
        isCurrentLocation.hashCode;
  }

  @override
  String toString() {
    return 'Location(latitude: $latitude, longitude: $longitude, cityName: $cityName, countryName: $countryName, countryCode: $countryCode, isCurrentLocation: $isCurrentLocation)';
  }

  Location copyWith({
    double? latitude,
    double? longitude,
    String? cityName,
    String? countryName,
    String? countryCode,
    bool? isCurrentLocation,
  }) {
    return Location(
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      cityName: cityName ?? this.cityName,
      countryName: countryName ?? this.countryName,
      countryCode: countryCode ?? this.countryCode,
      isCurrentLocation: isCurrentLocation ?? this.isCurrentLocation,
    );
  }
}
